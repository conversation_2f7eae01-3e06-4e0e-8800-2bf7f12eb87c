#!/usr/bin/env python3
"""
Test script to verify the supervisor thinking block fix is working correctly.
"""

import sys
import os

# Add the src directory to the path
#sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'agents'))

def test_supervisor_llm_configuration():
    """Test that the supervisor LLM is configured correctly without thinking mode."""
    
    print("=== Testing Supervisor LLM Configuration ===\n")
    
    try:
        from bond_ai.utils import load_chat_model_non_thinking
        from bond_ai.graph import supervisor_llm
        
        print("✓ supervisor_llm imported successfully")
        print(f"✓ supervisor_llm type: {type(supervisor_llm)}")
        
        # Check if the model is configured without thinking
        model_kwargs = getattr(supervisor_llm, 'model_kwargs', {})
        thinking_config = model_kwargs.get('thinking', None)
        
        if thinking_config is None:
            print("✓ No thinking configuration found (expected for non-thinking model)")
        else:
            print(f"⚠ Thinking configuration found: {thinking_config}")
            print("  This might cause issues if thinking blocks are required")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False

def test_supervisor_function_imports():
    """Test that the supervisor function can access all required imports."""
    
    print("\n=== Testing Supervisor Function Imports ===\n")
    
    try:
        from bond_ai.graph import supervisor_agent_anthropic
        from bond_ai.prompts_v1 import SUPERVISOR_AGENT_PROMPT
        from bond_ai.registry import agent_registry
        
        print("✓ supervisor_agent_anthropic function imported")
        print("✓ SUPERVISOR_AGENT_PROMPT imported")
        print("✓ agent_registry imported")
        
        # Test that the function exists and is callable
        if callable(supervisor_agent_anthropic):
            print("✓ supervisor_agent_anthropic is callable")
        else:
            print("✗ supervisor_agent_anthropic is not callable")
            return False
        
        # Test that the prompt contains expected content
        if "ROLE & RESPONSIBILITIES" in SUPERVISOR_AGENT_PROMPT:
            print("✓ SUPERVISOR_AGENT_PROMPT contains expected content")
        else:
            print("✗ SUPERVISOR_AGENT_PROMPT missing expected content")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        return False

def test_mock_supervisor_call():
    """Test a mock supervisor call to verify the structure works."""
    
    print("\n=== Testing Mock Supervisor Call Structure ===\n")
    
    try:
        from bond_ai.registry import agent_registry
        from bond_ai.prompts_v1 import SUPERVISOR_AGENT_PROMPT
        from datetime import datetime
        
        # Mock state object
        mock_state = {
            'table_summary': 'Test table with 10 prospects',
            'selected_row_ids': '1,2,3',
            'selected_column_ids': 'name,email',
            'mode': 'tool',
            'messages': []
        }
        
        # Test agent directory generation (like in supervisor function)
        current_members = agent_registry.get_agent_names()
        current_options = ["FINISH"] + current_members
        
        print(f"✓ Found {len(current_members)} agents in registry")
        print(f"✓ Routing options: {current_options}")
        
        # Test dynamic agent directory building
        agent_directory_entries = []
        react_agents = agent_registry.get_react_agents()
        custom_nodes = agent_registry.get_custom_nodes()
        
        for name, config in react_agents.items():
            description = config.get('description', 'ReAct agent')
            tools = config.get('tools', [])
            
            tool_names = []
            for tool in tools:
                if hasattr(tool, 'name'):
                    tool_names.append(tool.name)
                elif hasattr(tool, '__name__'):
                    tool_names.append(tool.__name__)
                else:
                    tool_names.append(str(tool))
            
            tools_str = ', '.join(tool_names) if tool_names else 'No specific tools'
            agent_entry = f"- **{name}**: {description} (Tools: {tools_str})"
            agent_directory_entries.append(agent_entry)
        
        for name, config in custom_nodes.items():
            description = config.get('description', 'Custom node function')
            agent_entry = f"- **{name}**: {description} (Type: Custom node function)"
            agent_directory_entries.append(agent_entry)
        
        agent_directory = f"""**AVAILABLE SPECIALIZED AGENTS:**
{chr(10).join(agent_directory_entries)}"""
        
        print("✓ Agent directory generated successfully")
        print(f"  Directory entries: {len(agent_directory_entries)}")
        
        # Test context variable extraction
        table_id = mock_state.get('table_id', 'Not available')
        today_date = datetime.now().strftime('%Y-%m-%d')
        current_filters = mock_state.get('current_filters', 'Not available')
        table_summary = mock_state.get('table_summary', 'Not available')
        selected_row_ids = mock_state.get('selected_row_ids', 'None')
        selected_column_ids = mock_state.get('selected_column_ids', 'None')
        mode = mock_state.get('mode', 'Not available')
        
        print("✓ Context variables extracted successfully")
        print(f"  Today's date: {today_date}")
        print(f"  Table summary: {table_summary}")
        
        # Test prompt formatting
        formatted_prompt = SUPERVISOR_AGENT_PROMPT.format(
            table_id=table_id,
            today_date=today_date,
            current_filters=current_filters,
            table_summary=table_summary,
            selected_row_ids=selected_row_ids,
            selected_column_ids=selected_column_ids,
            mode=mode,
            agent_directory=agent_directory
        )
        
        print("✓ Supervisor prompt formatted successfully")
        print(f"  Formatted prompt length: {len(formatted_prompt)} characters")
        
        return True
        
    except Exception as e:
        print(f"✗ Mock test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success3 = test_mock_supervisor_call()
    # success1 = test_supervisor_llm_configuration()
    # success2 = test_supervisor_function_imports()
    # success3 = test_mock_supervisor_call()
    
    # if success1 and success2 and success3:
    #     print("\n🎉 All supervisor fix tests passed! The thinking block issue should be resolved.")
    #     print("\nKey changes made:")
    #     print("- Added supervisor_llm using load_chat_model_non_thinking()")
    #     print("- Updated supervisor_agent_anthropic to use supervisor_llm instead of llm")
    #     print("- This avoids the thinking block requirement for supervisor routing decisions")
    # else:
    #     print("\n❌ Some tests failed. Please check the implementation.")
